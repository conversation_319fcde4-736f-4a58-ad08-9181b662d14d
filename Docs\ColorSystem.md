# FluentSystemDesign 色彩系统

FluentSystemDesign WPF控件库提供了一套完整的色彩系统，遵循Fluent Design System的设计原则，确保在不同主题下都能提供优秀的视觉体验和可访问性。

## 目录

- [色彩调色板](#色彩调色板)
- [主题支持](#主题支持)
- [语义化颜色](#语义化颜色)
- [使用指南](#使用指南)
- [主题切换](#主题切换)
- [可访问性](#可访问性)
- [最佳实践](#最佳实践)

## 色彩调色板

### 主色调 (Primary)
主色调基于蓝色系，用于主要的交互元素和品牌识别。

| 色阶 | 颜色值 | 用途 |
|------|--------|------|
| Primary50 | #E3F2FD | 极浅背景 |
| Primary100 | #BBDEFB | 浅背景 |
| Primary200 | #90CAF9 | 悬停状态 |
| Primary300 | #64B5F6 | 次要元素 |
| Primary400 | #42A5F5 | 深色主题主色 |
| Primary500 | #2196F3 | 浅色主题主色 |
| Primary600 | #1E88E5 | 按下状态 |
| Primary700 | #1976D2 | 深色变体 |
| Primary800 | #1565C0 | 更深变体 |
| Primary900 | #0D47A1 | 最深变体 |

### 次要色调 (Secondary)
次要色调基于紫色系，用于辅助元素和次要操作。

| 色阶 | 颜色值 | 用途 |
|------|--------|------|
| Secondary50 | #F3E5F5 | 极浅背景 |
| Secondary100 | #E1BEE7 | 浅背景 |
| Secondary200 | #CE93D8 | 悬停状态 |
| Secondary300 | #BA68C8 | 次要元素 |
| Secondary400 | #AB47BC | 深色主题次要色 |
| Secondary500 | #9C27B0 | 浅色主题次要色 |
| Secondary600 | #8E24AA | 按下状态 |
| Secondary700 | #7B1FA2 | 深色变体 |
| Secondary800 | #6A1B9A | 更深变体 |
| Secondary900 | #4A148C | 最深变体 |

### 强调色 (Accent)
强调色基于橙色系，用于突出显示和吸引注意力的元素。

| 色阶 | 颜色值 | 用途 |
|------|--------|------|
| Accent50 | #FFF3E0 | 极浅背景 |
| Accent100 | #FFE0B2 | 浅背景 |
| Accent200 | #FFCC80 | 悬停状态 |
| Accent300 | #FFB74D | 次要元素 |
| Accent400 | #FFA726 | 深色主题强调色 |
| Accent500 | #FF9800 | 浅色主题强调色 |
| Accent600 | #FB8C00 | 按下状态 |
| Accent700 | #F57C00 | 深色变体 |
| Accent800 | #EF6C00 | 更深变体 |
| Accent900 | #E65100 | 最深变体 |

### 中性色 (Neutral)
中性色基于灰色系，用于文本、边框、背景等基础元素。

| 色阶 | 颜色值 | 用途 |
|------|--------|------|
| Neutral50 | #FAFAFA | 浅色主题背景 |
| Neutral100 | #F5F5F5 | 控件悬停背景 |
| Neutral200 | #EEEEEE | 分割线、边框 |
| Neutral300 | #E0E0E0 | 输入框边框 |
| Neutral400 | #BDBDBD | 禁用文本 |
| Neutral500 | #9E9E9E | 次要文本 |
| Neutral600 | #757575 | 三级文本 |
| Neutral700 | #616161 | 二级文本 |
| Neutral800 | #424242 | 主要文本 |
| Neutral900 | #212121 | 最深文本 |

## 主题支持

### 浅色主题 (Light Theme)
浅色主题适用于明亮环境，提供清晰的视觉层次和良好的可读性。

**主要特征：**
- 白色或浅色背景
- 深色文本确保高对比度
- 使用Primary500作为主色调
- 边框和分割线使用浅灰色

### 深色主题 (Dark Theme)
深色主题适用于低光环境，减少眼部疲劳，提供现代化的视觉体验。

**主要特征：**
- 深色背景（#121212基础色）
- 浅色文本确保可读性
- 使用Primary400作为主色调
- 边框和分割线使用深灰色

## 语义化颜色

### 成功色 (Success)
基于绿色系，用于表示成功状态、完成操作等正面反馈。

- **Success500**: #4CAF50 (浅色主题)
- **Success400**: #66BB6A (深色主题)

### 警告色 (Warning)
基于琥珀色系，用于表示警告信息、需要注意的状态。

- **Warning500**: #FF8F00 (浅色主题)
- **Warning400**: #FFB300 (深色主题)

### 错误色 (Error)
基于红色系，用于表示错误状态、失败操作等负面反馈。

- **Error500**: #F44336 (浅色主题)
- **Error400**: #EF5350 (深色主题)

### 信息色 (Info)
基于青色系，用于表示信息提示、帮助说明等中性反馈。

- **Info500**: #00BCD4 (浅色主题)
- **Info400**: #26C6DA (深色主题)

## 使用指南

### 在XAML中使用颜色

```xml
<!-- 使用主题色画刷 -->
<Button Background="{StaticResource PrimaryBrush}" 
        Foreground="{StaticResource TextOnPrimaryBrush}"/>

<!-- 使用语义化颜色 -->
<TextBlock Foreground="{StaticResource ErrorBrush}" 
           Text="错误信息"/>

<!-- 使用中性色 -->
<Border BorderBrush="{StaticResource BorderBrush}" 
        BorderThickness="1"/>
```

### 在代码中使用颜色

```csharp
// 获取主题色画刷
var primaryBrush = Application.Current.FindResource("PrimaryBrush") as SolidColorBrush;

// 获取原始颜色值
var primaryColor = Application.Current.FindResource("Primary500") as Color?;
```

## 主题切换

使用`ThemeManager`类来管理主题切换：

```csharp
// 应用浅色主题
ThemeManager.ApplyTheme(Application.Current, ThemeManager.ThemeType.Light);

// 应用深色主题
ThemeManager.ApplyTheme(Application.Current, ThemeManager.ThemeType.Dark);

// 切换主题
ThemeManager.ToggleTheme(Application.Current);

// 获取系统主题偏好
var systemTheme = ThemeManager.GetSystemTheme();

// 监听主题变更事件
ThemeManager.ThemeChanged += (sender, e) =>
{
    Console.WriteLine($"主题从 {e.OldTheme} 切换到 {e.NewTheme}");
};
```

## 可访问性

### 对比度要求
所有颜色组合都符合WCAG 2.1 AA级对比度要求：

- **正常文本**: 对比度至少4.5:1
- **大文本**: 对比度至少3:1
- **UI组件**: 对比度至少3:1

### 色盲友好
色彩系统考虑了色盲用户的需求：

- 不仅依赖颜色传达信息
- 提供图标、文字等辅助标识
- 使用高对比度确保可读性

## 最佳实践

### 1. 颜色层次
- 使用主色调表示最重要的操作
- 使用次要色调表示次要操作
- 使用强调色突出特殊内容
- 使用中性色构建基础界面

### 2. 语义化使用
- 成功色仅用于正面反馈
- 错误色仅用于负面反馈
- 警告色用于需要注意的信息
- 信息色用于中性提示

### 3. 主题适配
- 确保控件在两种主题下都有良好表现
- 使用主题相关的画刷而非硬编码颜色
- 测试主题切换的视觉效果

### 4. 性能考虑
- 使用StaticResource而非DynamicResource（除非需要动态更新）
- 避免在代码中频繁查找资源
- 缓存常用的画刷对象

### 5. 自定义扩展
- 基于现有色阶创建自定义颜色
- 保持与整体色彩系统的一致性
- 确保自定义颜色符合可访问性要求

---

[返回主文档](../README.md)
