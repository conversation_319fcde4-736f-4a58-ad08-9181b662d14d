<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Merge base colors -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ========================================== -->
    <!-- Dark Theme Color Mappings -->
    <!-- ========================================== -->

    <!-- Primary Theme Colors -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource Primary400}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource Primary200}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource Primary600}"/>
    <SolidColorBrush x:Key="PrimaryHoverBrush" Color="{StaticResource Primary300}"/>
    <SolidColorBrush x:Key="PrimaryPressedBrush" Color="{StaticResource Primary500}"/>

    <!-- Secondary Theme Colors -->
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource Secondary400}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource Secondary200}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource Secondary600}"/>
    <SolidColorBrush x:Key="SecondaryHoverBrush" Color="{StaticResource Secondary300}"/>
    <SolidColorBrush x:Key="SecondaryPressedBrush" Color="{StaticResource Secondary500}"/>

    <!-- Accent Theme Colors -->
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource Accent400}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource Accent200}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource Accent600}"/>
    <SolidColorBrush x:Key="AccentHoverBrush" Color="{StaticResource Accent300}"/>
    <SolidColorBrush x:Key="AccentPressedBrush" Color="{StaticResource Accent500}"/>

    <!-- Background Colors -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="#121212"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#2D2D2D"/>
    <SolidColorBrush x:Key="DialogBackgroundBrush" Color="#2D2D2D"/>
    <SolidColorBrush x:Key="PopupBackgroundBrush" Color="#2D2D2D"/>

    <!-- Text Colors -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#B3FFFFFF"/>
    <SolidColorBrush x:Key="TextTertiaryBrush" Color="#80FFFFFF"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="#4DFFFFFF"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="#000000"/>
    <SolidColorBrush x:Key="TextOnSecondaryBrush" Color="#000000"/>
    <SolidColorBrush x:Key="TextOnAccentBrush" Color="#000000"/>

    <!-- Border Colors -->
    <SolidColorBrush x:Key="BorderBrush" Color="#404040"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="#333333"/>
    <SolidColorBrush x:Key="BorderDarkBrush" Color="#4D4D4D"/>
    <SolidColorBrush x:Key="DividerBrush" Color="#333333"/>

    <!-- Control Colors -->
    <SolidColorBrush x:Key="ControlBackgroundBrush" Color="#2D2D2D"/>
    <SolidColorBrush x:Key="ControlHoverBrush" Color="#404040"/>
    <SolidColorBrush x:Key="ControlPressedBrush" Color="#4D4D4D"/>
    <SolidColorBrush x:Key="ControlDisabledBrush" Color="#1A1A1A"/>
    <SolidColorBrush x:Key="ControlFocusBrush" Color="{StaticResource Primary400}"/>

    <!-- Input Colors -->
    <SolidColorBrush x:Key="InputBackgroundBrush" Color="#2D2D2D"/>
    <SolidColorBrush x:Key="InputBorderBrush" Color="#404040"/>
    <SolidColorBrush x:Key="InputHoverBorderBrush" Color="#4D4D4D"/>
    <SolidColorBrush x:Key="InputFocusBorderBrush" Color="{StaticResource Primary400}"/>
    <SolidColorBrush x:Key="InputDisabledBackgroundBrush" Color="#1A1A1A"/>
    <SolidColorBrush x:Key="InputDisabledBorderBrush" Color="#333333"/>

    <!-- Shadow Colors -->
    <SolidColorBrush x:Key="ShadowBrush" Color="#40000000"/>
    <SolidColorBrush x:Key="ShadowLightBrush" Color="#26000000"/>
    <SolidColorBrush x:Key="ShadowDarkBrush" Color="#59000000"/>

    <!-- Semantic State Colors -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource Success400}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="#1A4CAF50"/>
    <SolidColorBrush x:Key="SuccessTextBrush" Color="{StaticResource Success300}"/>

    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource Warning400}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="#1AFF8F00"/>
    <SolidColorBrush x:Key="WarningTextBrush" Color="{StaticResource Warning300}"/>

    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource Error400}"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="#1AF44336"/>
    <SolidColorBrush x:Key="ErrorTextBrush" Color="{StaticResource Error300}"/>

    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource Info400}"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="#1A00BCD4"/>
    <SolidColorBrush x:Key="InfoTextBrush" Color="{StaticResource Info300}"/>

    <!-- Navigation Colors -->
    <SolidColorBrush x:Key="NavigationBackgroundBrush" Color="#1E1E1E"/>
    <SolidColorBrush x:Key="NavigationItemHoverBrush" Color="#404040"/>
    <SolidColorBrush x:Key="NavigationItemSelectedBrush" Color="#1A2196F3"/>
    <SolidColorBrush x:Key="NavigationItemPressedBrush" Color="#4D4D4D"/>

    <!-- Menu Colors -->
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="#2D2D2D"/>
    <SolidColorBrush x:Key="MenuItemHoverBrush" Color="#404040"/>
    <SolidColorBrush x:Key="MenuItemPressedBrush" Color="#4D4D4D"/>
    <SolidColorBrush x:Key="MenuSeparatorBrush" Color="#404040"/>

    <!-- Overlay Colors -->
    <SolidColorBrush x:Key="OverlayBrush" Color="#80000000"/>
    <SolidColorBrush x:Key="OverlayLightBrush" Color="#40000000"/>

</ResourceDictionary>
