{"version": 3, "targets": {"net8.0-windows7.0": {"FluentSystemDesign.WPF/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/FluentSystemDesign.WPF.dll": {}}, "runtime": {"bin/placeholder/FluentSystemDesign.WPF.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}}}, "libraries": {"FluentSystemDesign.WPF/1.0.0": {"type": "project", "path": "../FluentSystemDesign.WPF/FluentSystemDesign.WPF.csproj", "msbuildProject": "../FluentSystemDesign.WPF/FluentSystemDesign.WPF.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["FluentSystemDesign.WPF >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj", "projectName": "FluentSystemDesign.Examples", "projectPath": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.WPF\\FluentSystemDesign.WPF.csproj": {"projectPath": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.WPF\\FluentSystemDesign.WPF.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}