<Window x:Class="FluentSystemDesign.Examples.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="FluentSystemDesign Examples" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <!-- 主窗口布局将在后续开发中实现 -->
        <TextBlock Text="FluentSystemDesign WPF控件库示例应用" 
                   HorizontalAlignment="Center" 
                   VerticalAlignment="Center"
                   FontSize="24"/>
    </Grid>
</Window>
