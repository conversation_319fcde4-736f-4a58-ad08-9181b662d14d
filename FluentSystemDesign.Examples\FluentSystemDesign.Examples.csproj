<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyName>FluentSystemDesign.Examples</AssemblyName>
    <RootNamespace>FluentSystemDesign.Examples</RootNamespace>

    <StartupObject>FluentSystemDesign.Examples.App</StartupObject>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>FluentSystemDesign Examples</AssemblyTitle>
    <AssemblyDescription>Example application showcasing FluentSystemDesign WPF controls</AssemblyDescription>
    <AssemblyCompany>FluentSystemDesign Team</AssemblyCompany>
    <AssemblyProduct>FluentSystemDesign Examples</AssemblyProduct>
    <AssemblyCopyright>Copyright © FluentSystemDesign Team 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\FluentSystemDesign.WPF\FluentSystemDesign.WPF.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\**\*" />
  </ItemGroup>

</Project>
