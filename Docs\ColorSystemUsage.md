# 色彩系统使用指南

本文档提供了FluentSystemDesign色彩系统的详细使用指南和代码示例。

## 快速开始

### 1. 引用主题资源

在您的应用程序中引用FluentSystemDesign的主题资源：

```xml
<!-- App.xaml -->
<Application x:Class="YourApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 引用FluentSystemDesign主题 -->
                <ResourceDictionary Source="pack://application:,,,/FluentSystemDesign.WPF;component/Themes/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

### 2. 初始化主题

在应用程序启动时初始化主题：

```csharp
// App.xaml.cs
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        
        // 应用系统推荐的主题
        var systemTheme = ThemeManager.GetSystemTheme();
        ThemeManager.ApplyTheme(this, systemTheme);
    }
}
```

## 基础用法

### 使用主题色

```xml
<!-- 主色调按钮 -->
<Button Content="主要操作" 
        Background="{StaticResource PrimaryBrush}"
        Foreground="{StaticResource TextOnPrimaryBrush}"/>

<!-- 次要色调按钮 -->
<Button Content="次要操作" 
        Background="{StaticResource SecondaryBrush}"
        Foreground="{StaticResource TextOnSecondaryBrush}"/>

<!-- 强调色按钮 -->
<Button Content="强调操作" 
        Background="{StaticResource AccentBrush}"
        Foreground="{StaticResource TextOnAccentBrush}"/>
```

### 使用语义化颜色

```xml
<!-- 成功状态 -->
<TextBlock Text="操作成功" 
           Foreground="{StaticResource SuccessBrush}"/>

<!-- 警告状态 -->
<Border Background="{StaticResource WarningLightBrush}"
        BorderBrush="{StaticResource WarningBrush}"
        BorderThickness="1">
    <TextBlock Text="警告信息" 
               Foreground="{StaticResource WarningTextBrush}"/>
</Border>

<!-- 错误状态 -->
<TextBlock Text="错误信息" 
           Foreground="{StaticResource ErrorBrush}"/>

<!-- 信息状态 -->
<TextBlock Text="提示信息" 
           Foreground="{StaticResource InfoBrush}"/>
```

### 使用中性色

```xml
<!-- 主要文本 -->
<TextBlock Text="主要内容" 
           Foreground="{StaticResource TextPrimaryBrush}"/>

<!-- 次要文本 -->
<TextBlock Text="次要内容" 
           Foreground="{StaticResource TextSecondaryBrush}"/>

<!-- 三级文本 -->
<TextBlock Text="辅助内容" 
           Foreground="{StaticResource TextTertiaryBrush}"/>

<!-- 禁用文本 -->
<TextBlock Text="禁用内容" 
           Foreground="{StaticResource TextDisabledBrush}"/>
```

## 高级用法

### 主题切换

```csharp
public class MainViewModel : INotifyPropertyChanged
{
    private bool _isDarkTheme;
    
    public bool IsDarkTheme
    {
        get => _isDarkTheme;
        set
        {
            _isDarkTheme = value;
            OnPropertyChanged();
            
            // 切换主题
            var theme = value ? ThemeManager.ThemeType.Dark : ThemeManager.ThemeType.Light;
            ThemeManager.ApplyTheme(Application.Current, theme);
        }
    }
    
    public ICommand ToggleThemeCommand => new RelayCommand(() => IsDarkTheme = !IsDarkTheme);
}
```

```xml
<!-- 主题切换开关 -->
<ToggleButton IsChecked="{Binding IsDarkTheme}"
              Content="深色模式"
              Command="{Binding ToggleThemeCommand}"/>
```

### 监听主题变更

```csharp
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        
        // 监听主题变更事件
        ThemeManager.ThemeChanged += OnThemeChanged;
    }
    
    private void OnThemeChanged(object sender, ThemeChangedEventArgs e)
    {
        // 主题变更时的处理逻辑
        Console.WriteLine($"主题从 {e.OldTheme} 切换到 {e.NewTheme}");
        
        // 可以在这里执行主题相关的自定义逻辑
        UpdateThemeSpecificElements(e.NewTheme);
    }
    
    private void UpdateThemeSpecificElements(ThemeManager.ThemeType theme)
    {
        // 更新主题特定的元素
        if (theme == ThemeManager.ThemeType.Dark)
        {
            // 深色主题特定处理
        }
        else
        {
            // 浅色主题特定处理
        }
    }
}
```

### 动态获取颜色

```csharp
public class ColorHelper
{
    /// <summary>
    /// 获取主题色画刷
    /// </summary>
    public static SolidColorBrush GetPrimaryBrush()
    {
        return Application.Current.FindResource("PrimaryBrush") as SolidColorBrush;
    }
    
    /// <summary>
    /// 获取原始颜色值
    /// </summary>
    public static Color GetPrimaryColor()
    {
        var color = Application.Current.FindResource("Primary500") as Color?;
        return color ?? Colors.Blue;
    }
    
    /// <summary>
    /// 创建自定义透明度的画刷
    /// </summary>
    public static SolidColorBrush CreateTransparentBrush(string colorKey, double opacity)
    {
        var color = Application.Current.FindResource(colorKey) as Color?;
        if (color.HasValue)
        {
            var transparentColor = Color.FromArgb(
                (byte)(255 * opacity),
                color.Value.R,
                color.Value.G,
                color.Value.B);
            return new SolidColorBrush(transparentColor);
        }
        return new SolidColorBrush(Colors.Transparent);
    }
}
```

### 自定义控件样式

```xml
<!-- 自定义按钮样式 -->
<Style x:Key="CustomButtonStyle" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
    <Setter Property="BorderBrush" Value="{StaticResource PrimaryDarkBrush}"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
    <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}"/>
    
    <Style.Triggers>
        <Trigger Property="IsMouseOver" Value="True">
            <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
        </Trigger>
        <Trigger Property="IsPressed" Value="True">
            <Setter Property="Background" Value="{StaticResource PrimaryPressedBrush}"/>
        </Trigger>
        <Trigger Property="IsEnabled" Value="False">
            <Setter Property="Background" Value="{StaticResource ControlDisabledBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
        </Trigger>
    </Style.Triggers>
</Style>
```

## 色彩预览控件

使用ColorPalette控件展示色彩系统：

```xml
<local:ColorPalette Title="主色调" x:Name="PrimaryPalette"/>
```

```csharp
// 在代码中填充色彩数据
private void LoadColorPalette()
{
    PrimaryPalette.ColorItems.Clear();
    
    var primaryColors = new[]
    {
        new { Name = "Primary50", Key = "Primary50" },
        new { Name = "Primary100", Key = "Primary100" },
        new { Name = "Primary200", Key = "Primary200" },
        new { Name = "Primary300", Key = "Primary300" },
        new { Name = "Primary400", Key = "Primary400" },
        new { Name = "Primary500", Key = "Primary500" },
        new { Name = "Primary600", Key = "Primary600" },
        new { Name = "Primary700", Key = "Primary700" },
        new { Name = "Primary800", Key = "Primary800" },
        new { Name = "Primary900", Key = "Primary900" }
    };
    
    foreach (var colorInfo in primaryColors)
    {
        var color = (Color)Application.Current.FindResource(colorInfo.Key);
        PrimaryPalette.ColorItems.Add(new ColorItem
        {
            Name = colorInfo.Name,
            Color = color
        });
    }
}
```

## 最佳实践

### 1. 性能优化

```xml
<!-- 推荐：使用StaticResource -->
<Button Background="{StaticResource PrimaryBrush}"/>

<!-- 避免：使用DynamicResource（除非需要动态更新） -->
<Button Background="{DynamicResource PrimaryBrush}"/>
```

### 2. 可访问性

```xml
<!-- 确保足够的对比度 -->
<Button Background="{StaticResource PrimaryBrush}"
        Foreground="{StaticResource TextOnPrimaryBrush}"/>

<!-- 为状态提供多种视觉提示 -->
<StackPanel Orientation="Horizontal">
    <Path Data="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"
          Fill="{StaticResource SuccessBrush}"/>
    <TextBlock Text="操作成功" 
               Foreground="{StaticResource SuccessTextBrush}"/>
</StackPanel>
```

### 3. 主题一致性

```xml
<!-- 保持整个应用的主题一致性 -->
<Grid Background="{StaticResource BackgroundBrush}">
    <Border Background="{StaticResource SurfaceBrush}"
            BorderBrush="{StaticResource BorderBrush}"
            BorderThickness="1">
        <StackPanel Margin="{StaticResource MediumMargin}">
            <TextBlock Text="标题" 
                       Foreground="{StaticResource TextPrimaryBrush}"
                       FontSize="{StaticResource TitleFontSize}"/>
            <TextBlock Text="内容" 
                       Foreground="{StaticResource TextSecondaryBrush}"
                       FontSize="{StaticResource BodyFontSize}"/>
        </StackPanel>
    </Border>
</Grid>
```

---

[返回色彩系统文档](ColorSystem.md) | [返回主文档](../README.md)
