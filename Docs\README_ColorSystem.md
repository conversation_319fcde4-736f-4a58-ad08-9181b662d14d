# FluentSystemDesign 色彩系统

## 概述

FluentSystemDesign WPF控件库的色彩系统已成功创建，提供了完整的色彩调色板、主题支持和语义化颜色定义。该系统遵循Fluent Design System的设计原则，确保在不同主题下都能提供优秀的视觉体验和可访问性。

## 已创建的文件

### 核心色彩文件

1. **`FluentSystemDesign.WPF/Themes/Colors.xaml`**
   - 定义了所有基础颜色值
   - 包含主色调、次要色调、强调色、中性色和语义化颜色
   - 每种颜色都有从50到900的完整色阶

2. **`FluentSystemDesign.WPF/Themes/LightTheme.xaml`**
   - 浅色主题的色彩映射
   - 将基础颜色映射为具体的UI元素颜色
   - 适用于明亮环境的视觉设计

3. **`FluentSystemDesign.WPF/Themes/DarkTheme.xaml`**
   - 深色主题的色彩映射
   - 针对深色环境优化的颜色选择
   - 确保在深色背景下的可读性

4. **`FluentSystemDesign.WPF/Themes/Styles.xaml`**
   - 主样式文件，整合所有主题资源
   - 定义全局样式和通用资源
   - 包含字体、间距、圆角、阴影等设计令牌

### 主题管理

5. **`FluentSystemDesign.WPF/Helpers/ThemeManager.cs`**
   - 主题管理器类
   - 提供主题切换功能
   - 支持系统主题检测
   - 包含主题变更事件

### 色彩展示控件

6. **`FluentSystemDesign.WPF/Controls/ColorPalette.cs`**
   - 色彩调色板控件
   - 用于展示色彩系统
   - 支持色彩信息显示

7. **`FluentSystemDesign.WPF/Themes/Controls/ColorPalette.xaml`**
   - ColorPalette控件的样式定义
   - 提供美观的色彩展示界面

8. **`FluentSystemDesign.WPF/Converters/StringToVisibilityConverter.cs`**
   - 字符串到可见性转换器
   - 支持ColorPalette控件的条件显示

### 文档

9. **`Docs/ColorSystem.md`**
   - 色彩系统的完整文档
   - 包含色彩调色板、主题支持、使用指南等
   - 提供可访问性和最佳实践说明

10. **`Docs/ColorSystemUsage.md`**
    - 详细的使用指南和代码示例
    - 涵盖基础用法到高级应用
    - 包含性能优化和最佳实践

11. **`Docs/ColorSystemTest.xaml`**
    - 色彩系统测试页面
    - 用于验证色彩系统功能
    - 展示各种颜色的实际效果

## 色彩系统特性

### 🎨 完整的色彩调色板
- **主色调 (Primary)**: 蓝色系，10个色阶
- **次要色调 (Secondary)**: 紫色系，10个色阶
- **强调色 (Accent)**: 橙色系，10个色阶
- **中性色 (Neutral)**: 灰色系，10个色阶

### 🌓 双主题支持
- **浅色主题**: 适用于明亮环境
- **深色主题**: 适用于低光环境
- **动态切换**: 支持运行时主题切换
- **系统集成**: 自动检测系统主题偏好

### 📊 语义化颜色
- **成功色 (Success)**: 绿色系，表示正面反馈
- **警告色 (Warning)**: 琥珀色系，表示注意事项
- **错误色 (Error)**: 红色系，表示负面反馈
- **信息色 (Info)**: 青色系，表示中性提示

### ♿ 可访问性支持
- **高对比度**: 符合WCAG 2.1 AA级标准
- **色盲友好**: 不仅依赖颜色传达信息
- **文本层次**: 清晰的文本颜色层次

## 使用方法

### 1. 引用主题资源

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/FluentSystemDesign.WPF;component/Themes/Styles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 2. 初始化主题

```csharp
protected override void OnStartup(StartupEventArgs e)
{
    base.OnStartup(e);
    
    // 应用系统推荐的主题
    var systemTheme = ThemeManager.GetSystemTheme();
    ThemeManager.ApplyTheme(this, systemTheme);
}
```

### 3. 使用颜色

```xml
<!-- 使用主题色 -->
<Button Background="{StaticResource PrimaryBrush}"
        Foreground="{StaticResource TextOnPrimaryBrush}"/>

<!-- 使用语义化颜色 -->
<TextBlock Text="成功信息" 
           Foreground="{StaticResource SuccessBrush}"/>
```

### 4. 主题切换

```csharp
// 切换到深色主题
ThemeManager.ApplyTheme(Application.Current, ThemeManager.ThemeType.Dark);

// 切换主题
ThemeManager.ToggleTheme(Application.Current);
```

## 设计原则

### 1. 一致性
- 所有颜色都基于统一的色彩系统
- 在不同控件中保持一致的颜色使用

### 2. 层次性
- 通过颜色深浅表达信息层次
- 主要、次要、三级内容有明确的视觉区分

### 3. 功能性
- 语义化颜色有明确的含义
- 颜色选择支持用户的操作决策

### 4. 适应性
- 支持深色和浅色两种主题
- 在不同环境下都有良好的可读性

## 扩展指南

### 添加新颜色

1. 在`Colors.xaml`中定义基础颜色值
2. 在主题文件中添加对应的画刷映射
3. 更新文档说明新颜色的用途

### 创建自定义主题

1. 复制现有主题文件
2. 修改颜色映射
3. 在ThemeManager中添加新主题类型

### 集成新控件

1. 在控件样式中使用主题色画刷
2. 确保在两种主题下都有良好表现
3. 添加到主样式文件的引用列表

## 下一步计划

1. **控件集成**: 将色彩系统应用到具体控件
2. **动画支持**: 添加主题切换的过渡动画
3. **自定义主题**: 支持用户自定义主题色
4. **高对比度**: 添加高对比度主题支持
5. **示例应用**: 在示例应用中展示色彩系统

---

色彩系统已成功创建并可以开始使用。请参考[色彩系统文档](ColorSystem.md)和[使用指南](ColorSystemUsage.md)了解详细信息。
