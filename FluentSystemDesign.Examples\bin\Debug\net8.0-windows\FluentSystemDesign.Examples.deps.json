{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"FluentSystemDesign.Examples/1.0.0": {"dependencies": {"FluentSystemDesign.WPF": "1.0.0"}, "runtime": {"FluentSystemDesign.Examples.dll": {}}}, "FluentSystemDesign.WPF/1.0.0": {"runtime": {"FluentSystemDesign.WPF.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"FluentSystemDesign.Examples/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FluentSystemDesign.WPF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}