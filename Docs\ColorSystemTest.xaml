<UserControl x:Class="FluentSystemDesign.Examples.Pages.ColorSystemTest"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:FluentSystemDesign.WPF.Controls;assembly=FluentSystemDesign.WPF">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="{StaticResource LargeMargin}">
            
            <!-- 页面标题 -->
            <TextBlock Text="色彩系统测试"
                       FontSize="{StaticResource LargeTitleFontSize}"
                       FontWeight="{StaticResource BoldFontWeight}"
                       Foreground="{StaticResource TextPrimaryBrush}"
                       Margin="{StaticResource LargeMargin}"/>

            <!-- 主题切换 -->
            <Border Background="{StaticResource CardBackgroundBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource MediumCornerRadius}"
                    Padding="{StaticResource MediumPadding}"
                    Margin="{StaticResource MediumMargin}">
                <StackPanel>
                    <TextBlock Text="主题切换"
                               FontSize="{StaticResource TitleFontSize}"
                               FontWeight="{StaticResource MediumFontWeight}"
                               Foreground="{StaticResource TextPrimaryBrush}"
                               Margin="{StaticResource SmallMargin}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource SmallMargin}">
                        <Button Content="浅色主题" 
                                Background="{StaticResource PrimaryBrush}"
                                Foreground="{StaticResource TextOnPrimaryBrush}"
                                Padding="{StaticResource MediumPadding}"
                                Margin="{StaticResource SmallMargin}"/>
                        
                        <Button Content="深色主题" 
                                Background="{StaticResource SecondaryBrush}"
                                Foreground="{StaticResource TextOnSecondaryBrush}"
                                Padding="{StaticResource MediumPadding}"
                                Margin="{StaticResource SmallMargin}"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 主色调展示 -->
            <local:ColorPalette Title="主色调 (Primary)" x:Name="PrimaryPalette"
                                Margin="{StaticResource MediumMargin}"/>

            <!-- 次要色调展示 -->
            <local:ColorPalette Title="次要色调 (Secondary)" x:Name="SecondaryPalette"
                                Margin="{StaticResource MediumMargin}"/>

            <!-- 强调色展示 -->
            <local:ColorPalette Title="强调色 (Accent)" x:Name="AccentPalette"
                                Margin="{StaticResource MediumMargin}"/>

            <!-- 中性色展示 -->
            <local:ColorPalette Title="中性色 (Neutral)" x:Name="NeutralPalette"
                                Margin="{StaticResource MediumMargin}"/>

            <!-- 语义化颜色展示 -->
            <Border Background="{StaticResource CardBackgroundBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource MediumCornerRadius}"
                    Padding="{StaticResource MediumPadding}"
                    Margin="{StaticResource MediumMargin}">
                <StackPanel>
                    <TextBlock Text="语义化颜色"
                               FontSize="{StaticResource TitleFontSize}"
                               FontWeight="{StaticResource MediumFontWeight}"
                               Foreground="{StaticResource TextPrimaryBrush}"
                               Margin="{StaticResource SmallMargin}"/>
                    
                    <UniformGrid Columns="2" Margin="{StaticResource SmallMargin}">
                        <!-- 成功色 -->
                        <Border Background="{StaticResource SuccessLightBrush}"
                                BorderBrush="{StaticResource SuccessBrush}"
                                BorderThickness="1"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Padding="{StaticResource MediumPadding}"
                                Margin="{StaticResource SmallMargin}">
                            <StackPanel>
                                <TextBlock Text="成功" 
                                           FontWeight="{StaticResource MediumFontWeight}"
                                           Foreground="{StaticResource SuccessTextBrush}"/>
                                <TextBlock Text="操作已成功完成" 
                                           Foreground="{StaticResource SuccessTextBrush}"/>
                            </StackPanel>
                        </Border>

                        <!-- 警告色 -->
                        <Border Background="{StaticResource WarningLightBrush}"
                                BorderBrush="{StaticResource WarningBrush}"
                                BorderThickness="1"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Padding="{StaticResource MediumPadding}"
                                Margin="{StaticResource SmallMargin}">
                            <StackPanel>
                                <TextBlock Text="警告" 
                                           FontWeight="{StaticResource MediumFontWeight}"
                                           Foreground="{StaticResource WarningTextBrush}"/>
                                <TextBlock Text="请注意此操作的影响" 
                                           Foreground="{StaticResource WarningTextBrush}"/>
                            </StackPanel>
                        </Border>

                        <!-- 错误色 -->
                        <Border Background="{StaticResource ErrorLightBrush}"
                                BorderBrush="{StaticResource ErrorBrush}"
                                BorderThickness="1"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Padding="{StaticResource MediumPadding}"
                                Margin="{StaticResource SmallMargin}">
                            <StackPanel>
                                <TextBlock Text="错误" 
                                           FontWeight="{StaticResource MediumFontWeight}"
                                           Foreground="{StaticResource ErrorTextBrush}"/>
                                <TextBlock Text="操作执行失败" 
                                           Foreground="{StaticResource ErrorTextBrush}"/>
                            </StackPanel>
                        </Border>

                        <!-- 信息色 -->
                        <Border Background="{StaticResource InfoLightBrush}"
                                BorderBrush="{StaticResource InfoBrush}"
                                BorderThickness="1"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Padding="{StaticResource MediumPadding}"
                                Margin="{StaticResource SmallMargin}">
                            <StackPanel>
                                <TextBlock Text="信息" 
                                           FontWeight="{StaticResource MediumFontWeight}"
                                           Foreground="{StaticResource InfoTextBrush}"/>
                                <TextBlock Text="这是一条提示信息" 
                                           Foreground="{StaticResource InfoTextBrush}"/>
                            </StackPanel>
                        </Border>
                    </UniformGrid>
                </StackPanel>
            </Border>

            <!-- 文本层次展示 -->
            <Border Background="{StaticResource CardBackgroundBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource MediumCornerRadius}"
                    Padding="{StaticResource MediumPadding}"
                    Margin="{StaticResource MediumMargin}">
                <StackPanel>
                    <TextBlock Text="文本层次"
                               FontSize="{StaticResource TitleFontSize}"
                               FontWeight="{StaticResource MediumFontWeight}"
                               Foreground="{StaticResource TextPrimaryBrush}"
                               Margin="{StaticResource SmallMargin}"/>
                    
                    <TextBlock Text="主要文本 - 用于标题和重要内容" 
                               Foreground="{StaticResource TextPrimaryBrush}"
                               FontSize="{StaticResource BodyFontSize}"
                               Margin="{StaticResource SmallMargin}"/>
                    
                    <TextBlock Text="次要文本 - 用于正文和说明内容" 
                               Foreground="{StaticResource TextSecondaryBrush}"
                               FontSize="{StaticResource BodyFontSize}"
                               Margin="{StaticResource SmallMargin}"/>
                    
                    <TextBlock Text="三级文本 - 用于辅助信息和标签" 
                               Foreground="{StaticResource TextTertiaryBrush}"
                               FontSize="{StaticResource BodyFontSize}"
                               Margin="{StaticResource SmallMargin}"/>
                    
                    <TextBlock Text="禁用文本 - 用于不可用的内容" 
                               Foreground="{StaticResource TextDisabledBrush}"
                               FontSize="{StaticResource BodyFontSize}"
                               Margin="{StaticResource SmallMargin}"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>

</UserControl>
