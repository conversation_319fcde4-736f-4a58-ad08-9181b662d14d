<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>FluentSystemDesign.WPF</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>FluentSystemDesign Team</Authors>
    <Company>FluentSystemDesign</Company>
    <Product>FluentSystemDesign WPF Controls</Product>
    <Description>A modern WPF control library based on Microsoft's Fluent Design System</Description>
    <PackageTags>WPF;Fluent;Design;Controls;UI;XAML</PackageTags>
    <PackageProjectUrl>https://github.com/FluentSystemDesign/FluentSystemDesign.WPF</PackageProjectUrl>
    <RepositoryUrl>https://github.com/FluentSystemDesign/FluentSystemDesign.WPF</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <Copyright>Copyright © FluentSystemDesign Team 2024</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <ItemGroup>
    <Resource Include="Assets\**\*" />
  </ItemGroup>



</Project>
