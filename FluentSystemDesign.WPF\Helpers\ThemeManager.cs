using System;
using System.Linq;
using System.Windows;

namespace FluentSystemDesign.WPF.Helpers
{
    /// <summary>
    /// 主题管理器，用于管理应用程序的主题切换
    /// </summary>
    public static class ThemeManager
    {
        /// <summary>
        /// 主题类型枚举
        /// </summary>
        public enum ThemeType
        {
            /// <summary>
            /// 浅色主题
            /// </summary>
            Light,
            /// <summary>
            /// 深色主题
            /// </summary>
            Dark
        }

        /// <summary>
        /// 当前主题类型
        /// </summary>
        public static ThemeType CurrentTheme { get; private set; } = ThemeType.Light;

        /// <summary>
        /// 主题变更事件
        /// </summary>
        public static event EventHandler<ThemeChangedEventArgs> ThemeChanged;

        /// <summary>
        /// 应用主题到指定的应用程序
        /// </summary>
        /// <param name="app">目标应用程序</param>
        /// <param name="theme">要应用的主题类型</param>
        public static void ApplyTheme(Application app, ThemeType theme)
        {
            if (app == null)
                throw new ArgumentNullException(nameof(app));

            var oldTheme = CurrentTheme;
            CurrentTheme = theme;

            // 清除现有主题资源
            RemoveThemeResources(app);

            // 添加新主题资源
            AddThemeResources(app, theme);

            // 触发主题变更事件
            ThemeChanged?.Invoke(null, new ThemeChangedEventArgs(oldTheme, theme));
        }

        /// <summary>
        /// 切换主题（在浅色和深色之间切换）
        /// </summary>
        /// <param name="app">目标应用程序</param>
        public static void ToggleTheme(Application app)
        {
            var newTheme = CurrentTheme == ThemeType.Light ? ThemeType.Dark : ThemeType.Light;
            ApplyTheme(app, newTheme);
        }

        /// <summary>
        /// 获取系统主题偏好
        /// </summary>
        /// <returns>系统推荐的主题类型</returns>
        public static ThemeType GetSystemTheme()
        {
            try
            {
                // 尝试读取Windows系统主题设置
                var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
                if (key != null)
                {
                    var value = key.GetValue("AppsUseLightTheme");
                    if (value is int intValue)
                    {
                        return intValue == 1 ? ThemeType.Light : ThemeType.Dark;
                    }
                }
            }
            catch
            {
                // 如果无法读取注册表，返回默认主题
            }

            return ThemeType.Light;
        }

        /// <summary>
        /// 移除主题资源
        /// </summary>
        /// <param name="app">目标应用程序</param>
        private static void RemoveThemeResources(Application app)
        {
            var resourcesToRemove = new string[]
            {
                "pack://application:,,,/FluentSystemDesign.WPF;component/Themes/LightTheme.xaml",
                "pack://application:,,,/FluentSystemDesign.WPF;component/Themes/DarkTheme.xaml"
            };

            foreach (var resourceUri in resourcesToRemove)
            {
                var resourceDict = app.Resources.MergedDictionaries
                    .FirstOrDefault(d => d.Source?.ToString() == resourceUri);
                if (resourceDict != null)
                {
                    app.Resources.MergedDictionaries.Remove(resourceDict);
                }
            }
        }

        /// <summary>
        /// 添加主题资源
        /// </summary>
        /// <param name="app">目标应用程序</param>
        /// <param name="theme">主题类型</param>
        private static void AddThemeResources(Application app, ThemeType theme)
        {
            var themeUri = theme == ThemeType.Light
                ? "pack://application:,,,/FluentSystemDesign.WPF;component/Themes/LightTheme.xaml"
                : "pack://application:,,,/FluentSystemDesign.WPF;component/Themes/DarkTheme.xaml";

            var themeDict = new ResourceDictionary
            {
                Source = new Uri(themeUri, UriKind.Absolute)
            };

            app.Resources.MergedDictionaries.Add(themeDict);
        }
    }

    /// <summary>
    /// 主题变更事件参数
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧主题
        /// </summary>
        public ThemeManager.ThemeType OldTheme { get; }

        /// <summary>
        /// 新主题
        /// </summary>
        public ThemeManager.ThemeType NewTheme { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldTheme">旧主题</param>
        /// <param name="newTheme">新主题</param>
        public ThemeChangedEventArgs(ThemeManager.ThemeType oldTheme, ThemeManager.ThemeType newTheme)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
        }
    }
}
