<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FluentSystemDesign.WPF</name>
    </assembly>
    <members>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorPalette">
            <summary>
            色彩调色板控件，用于展示色彩系统
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorPalette.Title">
            <summary>
            调色板标题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorPalette.TitleProperty">
            <summary>
            标识 Title 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorPalette.ColorItems">
            <summary>
            色彩项集合
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorPalette.ColorItemsProperty">
            <summary>
            标识 ColorItems 依赖属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.ColorPalette.#ctor">
            <summary>
            初始化 ColorPalette 类的新实例
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorItem">
            <summary>
            色彩项
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Name">
            <summary>
            色彩名称
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.NameProperty">
            <summary>
            标识 Name 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Color">
            <summary>
            色彩值
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.ColorProperty">
            <summary>
            标识 Color 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Brush">
            <summary>
            色彩画刷
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.BrushProperty">
            <summary>
            标识 Brush 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.HexValue">
            <summary>
            十六进制色值
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.HexValueProperty">
            <summary>
            标识 HexValue 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.IsDark">
            <summary>
            是否为深色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.IsDarkProperty">
            <summary>
            标识 IsDark 依赖属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.ColorItem.#ctor">
            <summary>
            初始化 ColorItem 类的新实例
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorItemCollection">
            <summary>
            色彩项集合
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.StringToVisibilityConverter">
            <summary>
            字符串到可见性转换器
            空字符串或null转换为Collapsed，非空字符串转换为Visible
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.StringToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将字符串转换为可见性
            </summary>
            <param name="value">字符串值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.StringToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将可见性转换为字符串（不支持）
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeManager">
            <summary>
            主题管理器，用于管理应用程序的主题切换
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType">
            <summary>
            主题类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType.Light">
            <summary>
            浅色主题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType.Dark">
            <summary>
            深色主题
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeManager.CurrentTheme">
            <summary>
            当前主题类型
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeChanged">
            <summary>
            主题变更事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.ApplyTheme(System.Windows.Application,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            应用主题到指定的应用程序
            </summary>
            <param name="app">目标应用程序</param>
            <param name="theme">要应用的主题类型</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.ToggleTheme(System.Windows.Application)">
            <summary>
            切换主题（在浅色和深色之间切换）
            </summary>
            <param name="app">目标应用程序</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.GetSystemTheme">
            <summary>
            获取系统主题偏好
            </summary>
            <returns>系统推荐的主题类型</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.RemoveThemeResources(System.Windows.Application)">
            <summary>
            移除主题资源
            </summary>
            <param name="app">目标应用程序</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.AddThemeResources(System.Windows.Application,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            添加主题资源
            </summary>
            <param name="app">目标应用程序</param>
            <param name="theme">主题类型</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs">
            <summary>
            主题变更事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.OldTheme">
            <summary>
            旧主题
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.NewTheme">
            <summary>
            新主题
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.#ctor(FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            构造函数
            </summary>
            <param name="oldTheme">旧主题</param>
            <param name="newTheme">新主题</param>
        </member>
    </members>
</doc>
