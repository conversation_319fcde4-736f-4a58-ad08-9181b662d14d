<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================== -->
    <!-- FluentSystemDesign 主样式文件 -->
    <!-- 此文件整合了所有主题资源和控件样式 -->
    <!-- ========================================== -->

    <ResourceDictionary.MergedDictionaries>
        
        <!-- 基础色彩定义 -->
        <ResourceDictionary Source="Colors.xaml"/>
        
        <!-- 默认使用浅色主题 -->
        <!-- 注意：实际主题应通过ThemeManager动态加载 -->
        <ResourceDictionary Source="LightTheme.xaml"/>
        
        <!-- 控件样式字典 -->
        <!-- 当添加新控件时，在此处引用对应的样式文件 -->
        <ResourceDictionary Source="Controls/ColorPalette.xaml"/>
        <!--
        <ResourceDictionary Source="Controls/Button.xaml"/>
        <ResourceDictionary Source="Controls/TextBox.xaml"/>
        <ResourceDictionary Source="Controls/NavigationView.xaml"/>
        -->
        
    </ResourceDictionary.MergedDictionaries>

    <!-- ========================================== -->
    <!-- 全局样式定义 -->
    <!-- ========================================== -->

    <!-- 默认窗口样式 -->
    <Style TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- 默认用户控件样式 -->
    <Style TargetType="UserControl">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- 默认页面样式 -->
    <Style TargetType="Page">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ========================================== -->
    <!-- 通用资源定义 -->
    <!-- ========================================== -->

    <!-- 圆角半径 -->
    <CornerRadius x:Key="SmallCornerRadius">2</CornerRadius>
    <CornerRadius x:Key="MediumCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="SmallShadow" 
                      Color="Black" 
                      Opacity="0.1" 
                      BlurRadius="4" 
                      ShadowDepth="1" 
                      Direction="270"/>
    
    <DropShadowEffect x:Key="MediumShadow" 
                      Color="Black" 
                      Opacity="0.15" 
                      BlurRadius="8" 
                      ShadowDepth="2" 
                      Direction="270"/>
    
    <DropShadowEffect x:Key="LargeShadow" 
                      Color="Black" 
                      Opacity="0.2" 
                      BlurRadius="16" 
                      ShadowDepth="4" 
                      Direction="270"/>

    <!-- 动画持续时间 -->
    <Duration x:Key="FastAnimationDuration">0:0:0.15</Duration>
    <Duration x:Key="MediumAnimationDuration">0:0:0.25</Duration>
    <Duration x:Key="SlowAnimationDuration">0:0:0.35</Duration>

    <!-- 缓动函数 -->
    <CubicEase x:Key="StandardEase" EasingMode="EaseInOut"/>
    <QuadraticEase x:Key="AccelerateEase" EasingMode="EaseIn"/>
    <QuadraticEase x:Key="DecelerateEase" EasingMode="EaseOut"/>

    <!-- 字体大小 -->
    <system:Double x:Key="CaptionFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
    <system:Double x:Key="BodyFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="SubtitleFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">16</system:Double>
    <system:Double x:Key="TitleFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">20</system:Double>
    <system:Double x:Key="LargeTitleFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">28</system:Double>
    <system:Double x:Key="DisplayFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">40</system:Double>

    <!-- 字体粗细 -->
    <FontWeight x:Key="LightFontWeight">Light</FontWeight>
    <FontWeight x:Key="NormalFontWeight">Normal</FontWeight>
    <FontWeight x:Key="MediumFontWeight">Medium</FontWeight>
    <FontWeight x:Key="SemiBoldFontWeight">SemiBold</FontWeight>
    <FontWeight x:Key="BoldFontWeight">Bold</FontWeight>

    <!-- 间距 -->
    <Thickness x:Key="XSmallMargin">2</Thickness>
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="MediumMargin">8</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="XLargeMargin">24</Thickness>
    <Thickness x:Key="XXLargeMargin">32</Thickness>

    <Thickness x:Key="XSmallPadding">2</Thickness>
    <Thickness x:Key="SmallPadding">4</Thickness>
    <Thickness x:Key="MediumPadding">8</Thickness>
    <Thickness x:Key="LargePadding">16</Thickness>
    <Thickness x:Key="XLargePadding">24</Thickness>
    <Thickness x:Key="XXLargePadding">32</Thickness>

</ResourceDictionary>
