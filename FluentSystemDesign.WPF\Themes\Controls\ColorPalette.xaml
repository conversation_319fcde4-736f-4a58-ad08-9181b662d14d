<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:FluentSystemDesign.WPF.Controls">

    <!-- ColorPalette 控件样式 -->
    <Style TargetType="{x:Type local:ColorPalette}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:ColorPalette}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource MediumCornerRadius}"
                            Padding="{StaticResource MediumPadding}">
                        <StackPanel>
                            <!-- 标题 -->
                            <TextBlock Text="{TemplateBinding Title}"
                                       FontSize="{StaticResource SubtitleFontSize}"
                                       FontWeight="{StaticResource MediumFontWeight}"
                                       Foreground="{StaticResource TextPrimaryBrush}"
                                       Margin="{StaticResource SmallMargin}"
                                       Visibility="{Binding Title, RelativeSource={RelativeSource TemplatedParent}, 
                                                   Converter={StaticResource StringToVisibilityConverter}}"/>
                            
                            <!-- 色彩项列表 -->
                            <ItemsControl ItemsSource="{TemplateBinding ColorItems}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <WrapPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate DataType="{x:Type local:ColorItem}">
                                        <Border Background="{Binding Brush}"
                                                Width="80"
                                                Height="80"
                                                CornerRadius="{StaticResource SmallCornerRadius}"
                                                Margin="{StaticResource SmallMargin}"
                                                BorderBrush="{StaticResource BorderBrush}"
                                                BorderThickness="1"
                                                ToolTip="{Binding Name}">
                                            <Border.Effect>
                                                <DropShadowEffect Color="Black" 
                                                                  Opacity="0.1" 
                                                                  BlurRadius="4" 
                                                                  ShadowDepth="1" 
                                                                  Direction="270"/>
                                            </Border.Effect>
                                            
                                            <Grid>
                                                <StackPanel VerticalAlignment="Bottom"
                                                            Background="#80000000"
                                                            Margin="4">
                                                    <TextBlock Text="{Binding Name}"
                                                               FontSize="10"
                                                               FontWeight="Medium"
                                                               Foreground="White"
                                                               TextAlignment="Center"
                                                               TextTrimming="CharacterEllipsis"/>
                                                    <TextBlock Text="{Binding HexValue}"
                                                               FontSize="8"
                                                               Foreground="#CCFFFFFF"
                                                               TextAlignment="Center"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- 字符串到可见性转换器 -->
    <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>

</ResourceDictionary>
