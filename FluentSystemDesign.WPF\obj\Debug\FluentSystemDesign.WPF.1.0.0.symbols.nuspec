﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>FluentSystemDesign.WPF</id>
    <version>1.0.0</version>
    <authors>FluentSystemDesign Team</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/FluentSystemDesign/FluentSystemDesign.WPF</projectUrl>
    <description>A modern WPF control library based on Microsoft's Fluent Design System</description>
    <copyright>Copyright © FluentSystemDesign Team 2024</copyright>
    <tags>WPF Fluent Design Controls UI XAML</tags>
    <repository type="git" url="https://github.com/FluentSystemDesign/FluentSystemDesign.WPF" />
    <dependencies>
      <group targetFramework="net8.0-windows7.0" />
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net8.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WPF" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="D:\Project\05 FluentDesignWPF\FluentSystemDesign.WPF\bin\Debug\net8.0-windows\FluentSystemDesign.WPF.pdb" target="lib\net8.0-windows7.0\FluentSystemDesign.WPF.pdb" />
  </files>
</package>