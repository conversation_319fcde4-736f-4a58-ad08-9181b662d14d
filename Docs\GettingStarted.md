# FluentSystemDesign WPF 快速入门指南

## 概述

FluentSystemDesign.WPF 是一个基于微软 Fluent Design System 设计语言的现代化 WPF 控件库，为 WPF 应用程序提供一致性、美观的 UI 组件。

## 系统要求

- .NET 8.0 或更高版本
- Windows 7 SP1 或更高版本
- Visual Studio 2022 或更高版本（推荐）

## 安装

### 通过 NuGet 包管理器安装

```bash
Install-Package FluentSystemDesign.WPF
```

### 通过 .NET CLI 安装

```bash
dotnet add package FluentSystemDesign.WPF
```

## 基本使用

### 1. 引用命名空间

在 XAML 文件中添加命名空间引用：

```xml
xmlns:fluent="clr-namespace:FluentSystemDesign.WPF;assembly=FluentSystemDesign.WPF"
```

### 2. 应用主题

在 App.xaml 中引用控件库的主题资源：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- FluentSystemDesign 主题资源 -->
            <ResourceDictionary Source="pack://application:,,,/FluentSystemDesign.WPF;component/Themes/Styles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 3. 使用控件

```xml
<fluent:FluentButton Content="点击我" Style="{StaticResource PrimaryButton}"/>
```

## 下一步

- 查看 [控件文档](Controls/) 了解各个控件的详细用法
- 运行示例应用程序查看控件效果
- 阅读 [主题定制指南](ThemeCustomization.md) 了解如何自定义主题

## 获取帮助

如果您在使用过程中遇到问题，请：

1. 查看 [常见问题](FAQ.md)
2. 查看 [GitHub Issues](https://github.com/FluentSystemDesign/FluentSystemDesign.WPF/issues)
3. 提交新的 Issue 报告问题或建议
